---
# tasks file for ansible/roles/modules_base
#- name: Install boost
#  become: true
#  ansible.builtin.apt:
#    name:
#      - libboost-all-dev
#    state: latest
#    update_cache: false

- name: Install ffmpeg
  become: true
  ansible.builtin.shell:
    cmd: /opt/apollo/installers/install_ffmpeg.sh
    executable: /bin/bash
  args: []
  environment:
    INSTALL_ATOM: "ffmpeg-4.3.1"
    INSTALL_PREFIX: "/opt/apollo/sysroot"

#- name: Install proj
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_proj.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "proj-7.1.0"
#    INSTALL_PREFIX: "/opt/apollo/sysroot"

#- name: Install vtk
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_vtk.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "vtk-8.2.0"
#    INSTALL_PREFIX: "/opt/apollo/sysroot"

#- name: Install pcl
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_pcl.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "pcl-1.10.1"
#    INSTALL_PREFIX: "/opt/apollo/sysroot"
#
#- name: Install opencv
#  become: true
#  ansible.builtin.shell:
#    cmd: /opt/apollo/installers/install_opencv.sh
#    executable: /bin/bash
#  args: []
#  environment:
#    INSTALL_ATOM: "opencv-4.4.0"
#    INSTALL_PREFIX: "/opt/apollo/sysroot"
