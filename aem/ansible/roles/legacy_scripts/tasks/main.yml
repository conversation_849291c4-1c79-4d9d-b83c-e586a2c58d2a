---
# tasks file for ansible/roles/legacy_scripts

- name: ensure /opt/apollo exists
  become: true
  ansible.builtin.file:
    path: /opt/apollo
    state: directory

- name: sync rcfiles
  become: true
  ansible.posix.synchronize:
    src: rcfiles/
    dest: /opt/apollo/rcfiles/

# TODO: rewrite to ansible format
- name: sync installer scripts
  become: true
  ansible.posix.synchronize:
    src: installers/
    dest: /opt/apollo/installers/
