# -*- sh -*- (Ba<PERSON> only)
#
# Copyright 2018 The Bazel Authors. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Bash completion of Bazel commands.
#
# Provides command-completion for:
# - bazel prefix options (e.g. --host_jvm_args)
# - blaze command-set (e.g. build, test)
# - blaze command-specific options (e.g. --copts)
# - values for enum-valued options
# - package-names, exploring all package-path roots.
# - targets within packages.

# Environment variables for customizing behavior:
#
# BAZEL_COMPLETION_USE_QUERY - if "true", `bazel query` will be used for
# autocompletion; otherwise, a heuristic grep is used. This is more accurate
# than the heuristic grep, especially for strangely-formatted BUILD files. But
# it can be slower, especially if the Bazel server is busy, and more brittle, if
# the BUILD file contains serious errors. This is an experimental feature.
#
# BAZEL_COMPLETION_ALLOW_TESTS_FOR_RUN - if "true", autocompletion results for
# `bazel run` includes test targets. This is convenient for users who run a lot
# of tests/benchmarks locally with 'bazel run'.

_bazel_completion_use_query() {
  _bazel__is_true "${BAZEL_COMPLETION_USE_QUERY}"
}

_bazel_completion_allow_tests_for_run() {
  _bazel__is_true "${BAZEL_COMPLETION_ALLOW_TESTS_FOR_RUN}"
}
# -*- sh -*- (Bash only)
#
# Copyright 2015 The Bazel Authors. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# The template is expanded at build time using tables of commands/options
# derived from the bazel executable built in the same client; the expansion is
# written to bazel-complete.bash.
#
# Don't use this script directly. Generate the final script with
# bazel build //scripts:bash_completion instead.

# This script expects a header to be prepended to it that defines the following
# nullary functions:
#
# _bazel_completion_use_query - Has a successful exit code if
# BAZEL_COMPLETION_USE_QUERY is "true".
#
# _bazel_completion_allow_tests_for_run - Has a successful exit code if
# BAZEL_COMPLETION_ALLOW_TESTS_FOR_RUN is "true".

# The package path used by the completion routines.  Unfortunately
# this isn't necessarily the same as the actual package path used by
# Bazel, but that's ok.  (It's impossible for us to reliably know what
# the relevant package-path, so this is just a good guess.  Users can
# override it if they want.)
: ${BAZEL_COMPLETION_PACKAGE_PATH:=%workspace%}

# Some commands might interfer with the important one, so don't complete them
: ${BAZEL_IGNORED_COMMAND_REGEX:="__none__"}

# bazel & ibazel commands
: ${BAZEL:=bazel}
: ${IBAZEL:=ibazel}

# Pattern to match for looking for a target
#  BAZEL_BUILD_MATCH_PATTERN__* give the pattern for label-*
#  when looking in the build file.
#  BAZEL_QUERY_MATCH_PATTERN__* give the pattern for label-*
#  when using 'bazel query'.
# _RUNTEST is a special case for _bazel_completion_allow_tests_for_run.
: ${BAZEL_BUILD_MATCH_PATTERN__test:='(.*_test|test_suite)'}
: ${BAZEL_QUERY_MATCH_PATTERN__test:='(test|test_suite)'}
: ${BAZEL_BUILD_MATCH_PATTERN__bin:='.*_binary'}
: ${BAZEL_QUERY_MATCH_PATTERN__bin:='(binary)'}
: ${BAZEL_BUILD_MATCH_PATTERN_RUNTEST__bin:='(.*_(binary|test)|test_suite)'}
: ${BAZEL_QUERY_MATCH_PATTERN_RUNTEST__bin:='(binary|test)'}
: ${BAZEL_BUILD_MATCH_PATTERN__:='.*'}
: ${BAZEL_QUERY_MATCH_PATTERN__:=''}

# Usage: _bazel__get_rule_match_pattern <command>
# Determine what kind of rules to match, based on command.
_bazel__get_rule_match_pattern() {
  local var_name pattern
  if _bazel_completion_use_query; then
    var_name="BAZEL_QUERY_MATCH_PATTERN"
  else
    var_name="BAZEL_BUILD_MATCH_PATTERN"
  fi
  if [[ "$1" =~ ^label-?([a-z]*)$ ]]; then
    pattern=${BASH_REMATCH[1]:-}
    if _bazel_completion_allow_tests_for_run; then
      eval "echo \"\${${var_name}_RUNTEST__${pattern}:-\$${var_name}__${pattern}}\""
    else
      eval "echo \"\$${var_name}__${pattern}\""
    fi
  fi
}

# Compute workspace directory. Search for the innermost
# enclosing directory with a WORKSPACE file.
_bazel__get_workspace_path() {
  local workspace=$PWD
  while true; do
    if [ -f "${workspace}/WORKSPACE" ]; then
      break
    elif [ -z "$workspace" -o "$workspace" = "/" ]; then
      workspace=$PWD
      break;
    fi
    workspace=${workspace%/*}
  done
  echo $workspace
}


# Find the current piece of the line to complete, but only do word breaks at
# certain characters. In particular, ignore these: "':=
# This method also takes into account the current cursor position.
#
# Works with both bash 3 and 4! Bash 3 and 4 perform different word breaks when
# computing the COMP_WORDS array. We need this here because Bazel options are of
# the form --a=b, and labels of the form //some/label:target.
_bazel__get_cword() {
  local cur=${COMP_LINE:0:$COMP_POINT}
  # This expression finds the last word break character, as defined in the
  # COMP_WORDBREAKS variable, but without '=' or ':', which is not preceeded by
  # a slash. Quote characters are also excluded.
  local wordbreaks="$COMP_WORDBREAKS"
  wordbreaks="${wordbreaks//\'/}"
  wordbreaks="${wordbreaks//\"/}"
  wordbreaks="${wordbreaks//:/}"
  wordbreaks="${wordbreaks//=/}"
  local word_start=$(expr "$cur" : '.*[^\]['"${wordbreaks}"']')
  echo "${cur:$word_start}"
}


# Usage: _bazel__package_path <workspace> <displacement>
#
# Prints a list of package-path root directories, displaced using the
# current displacement from the workspace.  All elements have a
# trailing slash.
_bazel__package_path() {
  local workspace=$1 displacement=$2 root
  IFS=:
  for root in ${BAZEL_COMPLETION_PACKAGE_PATH//\%workspace\%/$workspace}; do
    unset IFS
    echo "$root/$displacement"
  done
}

# Usage: _bazel__options_for <command>
#
# Prints the set of options for a given Bazel command, e.g. "build".
_bazel__options_for() {
  local options
  if [[ "${BAZEL_COMMAND_LIST}" =~ ^(.* )?$1( .*)?$ ]]; then
      # assumes option names only use ASCII characters
      local option_name=$(echo $1 | tr a-z A-Z | tr "-" "_")
      eval "echo \${BAZEL_COMMAND_${option_name}_FLAGS}" | tr " " "\n"
  fi
}
# Usage: _bazel__expansion_for <command>
#
# Prints the completion pattern for a given Bazel command, e.g. "build".
_bazel__expansion_for() {
  local options
  if [[ "${BAZEL_COMMAND_LIST}" =~ ^(.* )?$1( .*)?$ ]]; then
      # assumes option names only use ASCII characters
      local option_name=$(echo $1 | tr a-z A-Z | tr "-" "_")
      eval "echo \${BAZEL_COMMAND_${option_name}_ARGUMENT}"
  fi
}

# Usage: _bazel__matching_targets <kind> <prefix>
#
# Prints target names of kind <kind> and starting with <prefix> in the BUILD
# file given as standard input.  <kind> is a basic regex (BRE) used to match the
# bazel rule kind and <prefix> is the prefix of the target name.
_bazel__matching_targets() {
  local kind_pattern="$1"
  local target_prefix="$2"
  # The following commands do respectively:
  #   Remove BUILD file comments
  #   Replace \n by spaces to have the BUILD file in a single line
  #   Extract all rule types and target names
  #   Grep the kind pattern and the target prefix
  #   Returns the target name
  sed 's/#.*$//' \
      | tr "\n" " " \
      | sed 's/\([a-zA-Z0-9_]*\) *(\([^)]* \)\{0,1\}name *= *['\''"]\([a-zA-Z0-9_/.+=,@~-]*\)['\''"][^)]*)/\
type:\1 name:\3\
/g' \
      | "grep" -E "^type:$kind_pattern name:$target_prefix" \
      | cut -d ':' -f 3
}


# Usage: _bazel__is_true <string>
#
# Returns true or false based on the input string. The following are
# valid true values (the rest are false): "1", "true".
_bazel__is_true() {
  local str="$1"
  [[ "$str" == "1" || "$str" == "true" ]]
}

# Usage: _bazel__expand_rules_in_package <workspace> <displacement>
#                                        <current> <label-type>
#
# Expands rules in specified packages, exploring all roots of
# $BAZEL_COMPLETION_PACKAGE_PATH, not just $(pwd).  Only rules
# appropriate to the command are printed.  Sets $COMPREPLY array to
# result.
#
# If _bazel_completion_use_query has a successful exit code, 'bazel query' is
# used instead, with the actual Bazel package path;
# $BAZEL_COMPLETION_PACKAGE_PATH is ignored in this case, since the actual Bazel
# value is likely to be more accurate.
_bazel__expand_rules_in_package() {
  local workspace=$1 displacement=$2 current=$3 label_type=$4
  local package_name=$(echo "$current" | cut -f1 -d:)
  local rule_prefix=$(echo "$current" | cut -f2 -d:)
  local root buildfile rule_pattern r result

  result=
  pattern=$(_bazel__get_rule_match_pattern "$label_type")
  if _bazel_completion_use_query; then
    package_name=$(echo "$package_name" | tr -d "'\"") # remove quotes
    result=$(${BAZEL} --output_base=/tmp/${BAZEL}-completion-$USER query \
                   --keep_going --noshow_progress --output=label \
      "kind('$pattern rule', '$package_name:*')" 2>/dev/null |
      cut -f2 -d: | "grep" "^$rule_prefix")
  else
    for root in $(_bazel__package_path "$workspace" "$displacement"); do
      buildfile="$root/$package_name/BUILD.bazel"
      if [ ! -f "$buildfile" ]; then
        buildfile="$root/$package_name/BUILD"
      fi
      if [ -f "$buildfile" ]; then
        result=$(_bazel__matching_targets \
                   "$pattern" "$rule_prefix" <"$buildfile")
        break
      fi
    done
  fi

  index=$(echo $result | wc -w)
  if [ -n "$result" ]; then
      echo "$result" | tr " " "\n" | sed 's|$| |'
  fi
  # Include ":all" wildcard if there was no unique match.  (The zero
  # case is tricky: we need to include "all" in that case since
  # otherwise we won't expand "a" to "all" in the absence of rules
  # starting with "a".)
  if [ $index -ne 1 ] && expr all : "\\($rule_prefix\\)" >/dev/null; then
    echo "all "
  fi
}

# Usage: _bazel__expand_package_name <workspace> <displacement> <current-word>
#                                    <label-type>
#
# Expands directories, but explores all roots of
# BAZEL_COMPLETION_PACKAGE_PATH, not just $(pwd).  When a directory is
# a bazel package, the completion offers "pkg:" so you can expand
# inside the package.
# Sets $COMPREPLY array to result.
_bazel__expand_package_name() {
  local workspace=$1 displacement=$2 current=$3 type=${4:-} root dir index
  for root in $(_bazel__package_path "$workspace" "$displacement"); do
    found=0
    for dir in $(compgen -d $root$current); do
      [ -L "$dir" ] && continue  # skip symlinks (e.g. bazel-bin)
      [[ "$dir" =~ ^(.*/)?\.[^/]*$ ]] && continue  # skip dotted dir (e.g. .git)
      found=1
      echo "${dir#$root}/"
      if [ -f $dir/BUILD.bazel -o -f $dir/BUILD ]; then
        if [ "${type}" = "label-package" ]; then
          echo "${dir#$root} "
        else
          echo "${dir#$root}:"
        fi
      fi
    done
    [ $found -gt 0 ] && break  # Stop searching package path upon first match.
  done
}

# Usage: _bazel__expand_target_pattern <workspace> <displacement>
#                                      <word> <label-syntax>
#
# Expands "word" to match target patterns, using the current workspace
# and displacement from it.  "command" is used to filter rules.
# Sets $COMPREPLY array to result.
_bazel__expand_target_pattern() {
  local workspace=$1 displacement=$2 current=$3 label_syntax=$4
  case "$current" in
    //*:*) # Expand rule names within package, no displacement.
      if [ "${label_syntax}" = "label-package" ]; then
        compgen -S " " -W "BUILD" "$(echo current | cut -f ':' -d2)"
      else
        _bazel__expand_rules_in_package "$workspace" "" "$current" "$label_syntax"
      fi
      ;;
    *:*) # Expand rule names within package, displaced.
      if [ "${label_syntax}" = "label-package" ]; then
        compgen -S " " -W "BUILD" "$(echo current | cut -f ':' -d2)"
      else
        _bazel__expand_rules_in_package \
          "$workspace" "$displacement" "$current" "$label_syntax"
      fi
      ;;
    //*) # Expand filenames using package-path, no displacement
      _bazel__expand_package_name "$workspace" "" "$current" "$label_syntax"
      ;;
    *) # Expand filenames using package-path, displaced.
      if [ -n "$current" ]; then
        _bazel__expand_package_name "$workspace" "$displacement" "$current" "$label_syntax"
      fi
      ;;
  esac
}

_bazel__get_command() {
  for word in "${COMP_WORDS[@]:1:COMP_CWORD-1}"; do
    if echo "$BAZEL_COMMAND_LIST" | "grep" -wsq -e "$word"; then
      echo $word
      break
    fi
  done
}

# Returns the displacement to the workspace given in $1
_bazel__get_displacement() {
  if [[ "$PWD" =~ ^$1/.*$ ]]; then
    echo ${PWD##$1/}/
  fi
}


# Usage: _bazel__complete_pattern <workspace> <displacement> <current>
#                                 <type>
#
# Expand a word according to a type. The currently supported types are:
#  - {a,b,c}: an enum that can take value a, b or c
#  - label: a label of any kind
#  - label-bin: a label to a runnable rule (basically to a _binary rule)
#  - label-test: a label to a test rule
#  - info-key: an info key as listed by `bazel help info-keys`
#  - command: the name of a command
#  - path: a file path
#  - combinaison of previous type using | as separator
_bazel__complete_pattern() {
  local workspace=$1 displacement=$2 current=$3 types=$4
  for type in $(echo $types | tr "|" "\n"); do
    case "$type" in
      label*)
        _bazel__expand_target_pattern "$workspace" "$displacement" \
            "$current" "$type"
        ;;
      info-key)
    compgen -S " " -W "${BAZEL_INFO_KEYS}" -- "$current"
        ;;
      "command")
        local commands=$(echo "${BAZEL_COMMAND_LIST}" \
          | tr " " "\n" | "grep" -v "^${BAZEL_IGNORED_COMMAND_REGEX}$")
    compgen -S " " -W "${commands}" -- "$current"
        ;;
      path)
        compgen -f -- "$current"
        ;;
      *)
        compgen -S " " -W "$type" -- "$current"
        ;;
    esac
  done
}

# Usage: _bazel__expand_options <workspace> <displacement> <current-word>
#                               <options>
#
# Expands options, making sure that if current-word contains an equals sign,
# it is handled appropriately.
_bazel__expand_options() {
  local workspace="$1" displacement="$2" cur="$3" options="$4"
  if [[ $cur =~ = ]]; then
    # also expands special labels
    current=$(echo "$cur" | cut -f2 -d=)
    _bazel__complete_pattern "$workspace" "$displacement" "$current" \
    "$(compgen -W "$options" -- "$cur" | cut -f2 -d=)" \
        | sort -u
  else
    compgen -W "$(echo "$options" | sed 's|=.*$|=|')" -- "$cur" \
    | sed 's|\([^=]\)$|\1 |'
  fi
}

# Usage: _bazel__abspath <file>
#
#
# Returns the absolute path to a file
_bazel__abspath() {
    echo "$(cd "$(dirname "$1")"; pwd)/$(basename "$1")"
 }

# Usage: _bazel__rc_imports <workspace> <rc-file>
#
#
# Returns the list of other RC imported (or try-imported) by a given RC file
# Only return files we can actually find, and only return absolute paths
_bazel__rc_imports() {
  local workspace="$1" rc_dir rc_file="$2" import_files
  rc_dir=$(dirname $rc_file)
  import_files=$(cat $rc_file \
      | sed 's/#.*//' \
      | sed -E "/^(try-){0,1}import/!d" \
      | sed -E "s/^(try-){0,1}import ([^ ]*).*$/\2/" \
      | sort -u)

  local confirmed_import_files=()
  for import in $import_files; do
    # rc imports can use %workspace% to refer to the workspace, and we need to substitute that here
    import=${import//\%workspace\%/$workspace}
    if [[ "${import:0:1}" != "/" ]] ; then
      import="$rc_dir/$import"
    fi
    import=$(_bazel__abspath $import)
    # Don't bother dealing with it further if we can't find it
    if [ -f "$import" ] ; then
      confirmed_import_files+=($import)
    fi
  done
  echo "${confirmed_import_files[@]}"
}

# Usage: _bazel__rc_expand_imports <workspace> <processed-rc-files ...> __new__ <new-rc-files ...>
#
#
# Function that receives a workspace and two lists. The first list is a list of RC files that have
# already been processed, and the second list contains new RC files that need processing. Each new file will be
# processed for "{try-}import" lines to discover more RC files that need parsing.
# Any lines we find in "{try-}import" will be checked against known files (and not processed again if known).
_bazel__rc_expand_imports() {
  local workspace="$1" rc_file new_found="no" processed_files=() to_process_files=() discovered_files=()
  # We've consumed workspace
  shift
  # Now grab everything else
  local all_files=($@)
  for rc_file in ${all_files[@]} ; do
    if [ "$rc_file" == "__new__" ] ; then
      new_found="yes"
      continue
    elif [ "$new_found" == "no" ] ; then
      processed_files+=($rc_file)
    else
      to_process_files+=($rc_file)
    fi
  done

  # For all the non-processed files, get the list of imports out of each of those files
  for rc_file in "${to_process_files[@]}"; do
    local potential_new_files+=($(_bazel__rc_imports "$workspace" "$rc_file"))
    processed_files+=($rc_file)
    for potential_new_file in ${potential_new_files[@]} ; do
      if [[ ! " ${processed_files[@]} " =~ " ${potential_new_file} " ]] ; then
        discovered_files+=($potential_new_file)
      fi
    done
  done

  # Finally, return two lists (separated by __new__) of the files that have been fully processed, and
  # the files that need processing.
  echo "${processed_files[@]}" "__new__" "${discovered_files[@]}"
}

# Usage: _bazel__rc_files <workspace>
#
#
# Returns the list of RC files to examine, given the current command-line args.
_bazel__rc_files() {
  local workspace="$1" new_files=() processed_files=()
  # Handle the workspace RC unless --noworkspace_rc says not to.
  if [[ ! "${COMP_LINE}" =~ "--noworkspace_rc" ]]; then
    local workspacerc="$workspace/.bazelrc"
    if [ -f "$workspacerc" ] ; then
      new_files+=($(_bazel__abspath $workspacerc))
    fi
  fi
  # Handle the $HOME RC unless --nohome_rc says not to.
  if [[ ! "${COMP_LINE}" =~ "--nohome_rc" ]]; then
    local homerc="$HOME/.bazelrc"
    if [ -f "$homerc" ] ; then
      new_files+=($(_bazel__abspath $homerc))
    fi
  fi
  # Handle the system level RC unless --nosystem_rc says not to.
  if [[ ! "${COMP_LINE}" =~ "--nosystem_rc" ]]; then
    local systemrc="/etc/bazel.bazelrc"
    if [ -f "$systemrc" ] ; then
      new_files+=($(_bazel__abspath $systemrc))
    fi
  fi
  # Finally, if the user specified any on the command-line, then grab those
  # keeping in mind that there may be several.
  if [[ "${COMP_LINE}" =~ "--bazelrc=" ]]; then
    # There's got to be a better way to do this, but... it gets the job done,
    # even if there are multiple --bazelrc on the command line. The sed command
    # SHOULD be simpler, but capturing several instances of the same pattern
    # from the same line is tricky because of the greedy nature of .*
    # Instead we transform it to multiple lines, and then back.
    local cmdlnrcs=$(echo ${COMP_LINE} | sed -E "s/--bazelrc=/\n--bazelrc=/g" | sed -E "/--bazelrc/!d;s/^--bazelrc=([^ ]*).*$/\1/g" | tr "\n" " ")
    for rc_file in $cmdlnrcs; do
      if [ -f "$rc_file" ] ; then
        new_files+=($(_bazel__abspath $rc_file))
      fi
    done
  fi

  # Each time we call _bazel__rc_expand_imports, it may find new files which then need to be expanded as well,
  # so we loop until we've processed all new files.
  while (( ${#new_files[@]} > 0 )) ; do
    local all_files=($(_bazel__rc_expand_imports "$workspace" "${processed_files[@]}" "__new__" "${new_files[@]}"))
    local new_found="no"
    new_files=()
    processed_files=()
    for file in ${all_files[@]} ; do
      if [ "$file" == "__new__" ] ; then
        new_found="yes"
        continue
      elif [ "$new_found" == "no" ] ; then
        processed_files+=($file)
      else
        new_files+=($file)
      fi
    done
  done

  echo "${processed_files[@]}"
}

# Usage: _bazel__all_configs <workspace> <command>
#
#
# Gets contents of all RC files and searches them for config names
# that could be used for expansion.
_bazel__all_configs() {
  local workspace="$1" command="$2" rc_files

  # Start out getting a list of all RC files that we can look for configs in
  # This respects the various command line options documented at
  # https://docs.bazel.build/versions/2.0.0/guide.html#bazelrc
  rc_files=$(_bazel__rc_files "$workspace")

  # Commands can inherit configs from other commands, so build up command_match, which is
  # a match list of the various commands that we can match against, given the command
  # specified by the user
  local build_inherit=("aquery" "clean" "coverage" "cquery" "info" "mobile-install" "print_action" "run" "test")
  local test_inherit=("coverage")
  local command_match="$command"
  if [[ "${build_inherit[@]}" =~ "$command" ]]; then
    command_match="$command_match|build"
  fi
  if [[ "${test_inherit[@]}" =~ "$command" ]]; then
    command_match="$command_match|test"
  fi

  # The following commands do respectively:
  #   Gets the contents of all relevant/allowed RC files
  #   Remove file comments
  #   Filter only the configs relevant to the current command
  #   Extract the config names
  #   Filters out redundant names and returns the results
  cat $rc_files \
      | sed 's/#.*//' \
      | sed -E "/^($command_match):/!d" \
      | sed -E "s/^($command_match):([^ ]*).*$/\2/" \
      | sort -u
}

# Usage: _bazel__expand_config <workspace> <command> <current-word>
#
#
# Expands configs, checking through the allowed rc files and parsing for configs
# relevant to the current command
_bazel__expand_config() {
  local workspace="$1" command="$2" cur="$3" rc_files all_configs
  all_configs=$(_bazel__all_configs "$workspace" "$command")
  compgen -S " " -W "$all_configs" -- "$cur"
}

_bazel__complete_stdout() {
  local cur=$(_bazel__get_cword) word command displacement workspace

  # Determine command: "" (startup-options) or one of $BAZEL_COMMAND_LIST.
  command="$(_bazel__get_command)"

  workspace="$(_bazel__get_workspace_path)"
  displacement="$(_bazel__get_displacement ${workspace})"

  case "$command" in
    "") # Expand startup-options or commands
      local commands=$(echo "${BAZEL_COMMAND_LIST}" \
        | tr " " "\n" | "grep" -v "^${BAZEL_IGNORED_COMMAND_REGEX}$")
      _bazel__expand_options  "$workspace" "$displacement" "$cur" \
          "${commands}\
          ${BAZEL_STARTUP_OPTIONS}"
      ;;

    *)
      case "$cur" in
        --config=*) # Expand options:
          _bazel__expand_config  "$workspace" "$command" "${cur#"--config="}"
          ;;
        -*) # Expand options:
          _bazel__expand_options  "$workspace" "$displacement" "$cur" \
              "$(_bazel__options_for $command)"
          ;;
        *)  # Expand target pattern
      expansion_pattern="$(_bazel__expansion_for $command)"
          NON_QUOTE_REGEX="^[\"']"
          if [[ $command = query && $cur =~ $NON_QUOTE_REGEX ]]; then
            : # Ideally we would expand query expressions---it's not
              # that hard, conceptually---but readline is just too
              # damn complex when it comes to quotation.  Instead,
              # for query, we just expand target patterns, unless
              # the first char is a quote.
          elif [ -n "$expansion_pattern" ]; then
            _bazel__complete_pattern \
        "$workspace" "$displacement" "$cur" "$expansion_pattern"
          fi
          ;;
      esac
      ;;
  esac
}

_bazel__to_compreply() {
  local replies="$1"
  COMPREPLY=()
  # Trick to preserve whitespaces
  while IFS="" read -r reply; do
    COMPREPLY+=("${reply}")
  done < <(echo "${replies}")
  # Null may be set despite there being no completions
  if [ ${#COMPREPLY[@]} -eq 1 ] && [ -z ${COMPREPLY[0]} ]; then
    COMPREPLY=()
  fi
}

_bazel__complete() {
  _bazel__to_compreply "$(_bazel__complete_stdout)"
}

# Some users have aliases such as bt="bazel test" or bb="bazel build", this
# completion function allows them to have auto-completion for these aliases.
_bazel__complete_target_stdout() {
  local cur=$(_bazel__get_cword) word command displacement workspace

  # Determine command: "" (startup-options) or one of $BAZEL_COMMAND_LIST.
  command="$1"

  workspace="$(_bazel__get_workspace_path)"
  displacement="$(_bazel__get_displacement ${workspace})"

  _bazel__to_compreply "$(_bazel__expand_target_pattern "$workspace" "$displacement" \
      "$cur" "$(_bazel__expansion_for $command)")"
}

# default completion for bazel
complete -F _bazel__complete -o nospace "${BAZEL}"
complete -F _bazel__complete -o nospace "${IBAZEL}"
BAZEL_COMMAND_LIST="analyze-profile aquery build canonicalize-flags clean config coverage cquery dump fetch help info license mobile-install print_action query run shutdown sync test version"
BAZEL_INFO_KEYS="
workspace
install_base
output_base
execution_root
output_path
client-env
bazel-bin
bazel-genfiles
bazel-testlogs
release
server_pid
server_log
package_path
used-heap-size
used-heap-size-after-gc
committed-heap-size
max-heap-size
gc-time
gc-count
java-runtime
java-vm
java-home
character-encoding
defaults-package
build-language
default-package-path
starlark-semantics
"
BAZEL_STARTUP_OPTIONS="
--batch
--nobatch
--batch_cpu_scheduling
--nobatch_cpu_scheduling
--bazelrc=
--block_for_lock
--noblock_for_lock
--client_debug
--noclient_debug
--connect_timeout_secs=
--deep_execroot
--nodeep_execroot
--expand_configs_in_place
--noexpand_configs_in_place
--failure_detail_out=path
--home_rc
--nohome_rc
--host_jvm_args=
--host_jvm_debug
--host_jvm_profile=
--idle_server_tasks
--noidle_server_tasks
--ignore_all_rc_files
--noignore_all_rc_files
--incompatible_enable_execution_transition
--noincompatible_enable_execution_transition
--io_nice_level=
--macos_qos_class=
--max_idle_secs=
--output_base=path
--output_user_root=path
--server_javabase=
--server_jvm_out=path
--shutdown_on_low_sys_mem
--noshutdown_on_low_sys_mem
--system_rc
--nosystem_rc
--unlimit_coredumps
--nounlimit_coredumps
--watchfs
--nowatchfs
--windows_enable_symlinks
--nowindows_enable_symlinks
--workspace_rc
--noworkspace_rc
"
BAZEL_COMMAND_ANALYZE_PROFILE_ARGUMENT="path"
BAZEL_COMMAND_ANALYZE_PROFILE_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--distdir=
--dump=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--repository_cache=path
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_AQUERY_ARGUMENT="label"
BAZEL_COMMAND_AQUERY_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspect_deps={off,conservative,precise}
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--implicit_deps
--noimplicit_deps
--include_artifacts
--noinclude_artifacts
--include_aspects
--noinclude_aspects
--include_commandline
--noinclude_commandline
--include_param_files
--noinclude_param_files
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_proto_output_v2
--noincompatible_proto_output_v2
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--infer_universe_scope
--noinfer_universe_scope
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--nodep_deps
--nonodep_deps
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto:default_values
--noproto:default_values
--proto:definition_stack
--noproto:definition_stack
--proto:flatten_selects
--noproto:flatten_selects
--proto:include_synthetic_attribute_hash
--noproto:include_synthetic_attribute_hash
--proto:instantiation_stack
--noproto:instantiation_stack
--proto:locations
--noproto:locations
--proto:output_rule_attrs=
--proto:rule_inputs_and_outputs
--noproto:rule_inputs_and_outputs
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--relative_locations
--norelative_locations
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--skyframe_state
--noskyframe_state
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_deps
--notool_deps
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--universe_scope=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_BUILD_ARGUMENT="label"
BAZEL_COMMAND_BUILD_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_CANONICALIZE_FLAGS_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--aspect_deps={off,conservative,precise}
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--canonicalize_policy
--nocanonicalize_policy
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--deleted_packages=
--disk_cache=path
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_graphless_genquery_force_sort
--noexperimental_graphless_genquery_force_sort
--experimental_graphless_query={auto,yes,no}
--noexperimental_graphless_query
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--for_command=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--graph:conditional_edges_limit=
--graph:factored
--nograph:factored
--graph:node_limit=
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--implicit_deps
--noimplicit_deps
--include_aspects
--noinclude_aspects
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prefer_unordered_output
--noincompatible_prefer_unordered_output
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--infer_universe_scope
--noinfer_universe_scope
--invocation_policy=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--line_terminator_null
--noline_terminator_null
--loading_phase_threads=
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--nodep_deps
--nonodep_deps
--noorder_results
--null
--order_output={no,deps,auto,full}
--order_results
--output=
--override_repository=
--package_path=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--proto:default_values
--noproto:default_values
--proto:definition_stack
--noproto:definition_stack
--proto:flatten_selects
--noproto:flatten_selects
--proto:include_synthetic_attribute_hash
--noproto:include_synthetic_attribute_hash
--proto:instantiation_stack
--noproto:instantiation_stack
--proto:locations
--noproto:locations
--proto:output_rule_attrs=
--proto:rule_inputs_and_outputs
--noproto:rule_inputs_and_outputs
--query_file=
--relative_locations
--norelative_locations
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repository_cache=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--show_warnings
--noshow_warnings
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--strict_test_suite
--nostrict_test_suite
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_deps
--notool_deps
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--universe_scope=
--watchfs
--nowatchfs
--xml:default_values
--noxml:default_values
--xml:line_numbers
--noxml:line_numbers
"
BAZEL_COMMAND_CLEAN_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--async
--noasync
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--expunge
--noexpunge
--expunge_async
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_CONFIG_ARGUMENT="string"
BAZEL_COMMAND_CONFIG_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dump_all
--nodump_all
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output={text,json}
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_COVERAGE_ARGUMENT="label-test"
BAZEL_COMMAND_COVERAGE_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--print_relative_test_log_paths
--noprint_relative_test_log_paths
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--test_verbose_timeout_warnings
--notest_verbose_timeout_warnings
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--verbose_test_summary
--noverbose_test_summary
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_CQUERY_ARGUMENT="label"
BAZEL_COMMAND_CQUERY_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspect_deps={off,conservative,precise}
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--implicit_deps
--noimplicit_deps
--include_aspects
--noinclude_aspects
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--infer_universe_scope
--noinfer_universe_scope
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--nodep_deps
--nonodep_deps
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto:default_values
--noproto:default_values
--proto:definition_stack
--noproto:definition_stack
--proto:flatten_selects
--noproto:flatten_selects
--proto:include_configurations
--noproto:include_configurations
--proto:include_synthetic_attribute_hash
--noproto:include_synthetic_attribute_hash
--proto:instantiation_stack
--noproto:instantiation_stack
--proto:locations
--noproto:locations
--proto:output_rule_attrs=
--proto:rule_inputs_and_outputs
--noproto:rule_inputs_and_outputs
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--relative_locations
--norelative_locations
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_config_fragments={off,direct_host_only,direct,transitive}
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark:expr=
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_deps
--notool_deps
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--transitions={full,lite,none}
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--universe_scope=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_DUMP_FLAGS="
--action_cache
--noaction_cache
--action_graph=
--action_graph:include_artifacts
--noaction_graph:include_artifacts
--action_graph:include_cmdline
--noaction_graph:include_cmdline
--action_graph:targets=
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--packages
--nopackages
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--repository_cache=path
--rule_classes
--norule_classes
--rules
--norules
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--skyframe={off,summary,detailed}
--skylark_memory=
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_FETCH_ARGUMENT="label"
BAZEL_COMMAND_FETCH_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--deleted_packages=
--disk_cache=path
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--loading_phase_threads=
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--package_path=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repository_cache=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_HELP_ARGUMENT="command|{startup_options,target-syntax,info-keys}"
BAZEL_COMMAND_HELP_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--help_verbosity={long,medium,short}
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--logging=
--long
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--repository_cache=path
--short
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_INFO_ARGUMENT="info-key"
BAZEL_COMMAND_INFO_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_make_env
--noshow_make_env
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_LICENSE_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--repository_cache=path
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_MOBILE_INSTALL_ARGUMENT="label"
BAZEL_COMMAND_MOBILE_INSTALL_FLAGS="
--action_env=
--adb=
--adb_arg=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--debug_app
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device=
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental
--noincremental
--incremental_dexing
--noincremental_dexing
--incremental_install_verbosity=
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--mode={classic,classic_internal_test_do_not_use,skylark}
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--split_apks
--nosplit_apks
--stamp
--nostamp
--starlark_cpu_profile=
--start={no,cold,warm,debug}
--start_app
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_PRINT_ACTION_ARGUMENT="label"
BAZEL_COMMAND_PRINT_ACTION_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--print_action_mnemonics=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_QUERY_ARGUMENT="label"
BAZEL_COMMAND_QUERY_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--aspect_deps={off,conservative,precise}
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--deleted_packages=
--disk_cache=path
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_graphless_genquery_force_sort
--noexperimental_graphless_genquery_force_sort
--experimental_graphless_query={auto,yes,no}
--noexperimental_graphless_query
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--graph:conditional_edges_limit=
--graph:factored
--nograph:factored
--graph:node_limit=
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--implicit_deps
--noimplicit_deps
--include_aspects
--noinclude_aspects
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prefer_unordered_output
--noincompatible_prefer_unordered_output
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--infer_universe_scope
--noinfer_universe_scope
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--line_terminator_null
--noline_terminator_null
--loading_phase_threads=
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--nodep_deps
--nonodep_deps
--noorder_results
--null
--order_output={no,deps,auto,full}
--order_results
--output=
--override_repository=
--package_path=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--proto:default_values
--noproto:default_values
--proto:definition_stack
--noproto:definition_stack
--proto:flatten_selects
--noproto:flatten_selects
--proto:include_synthetic_attribute_hash
--noproto:include_synthetic_attribute_hash
--proto:instantiation_stack
--noproto:instantiation_stack
--proto:locations
--noproto:locations
--proto:output_rule_attrs=
--proto:rule_inputs_and_outputs
--noproto:rule_inputs_and_outputs
--query_file=
--relative_locations
--norelative_locations
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repository_cache=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--strict_test_suite
--nostrict_test_suite
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_deps
--notool_deps
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--universe_scope=
--watchfs
--nowatchfs
--xml:default_values
--noxml:default_values
--xml:line_numbers
--noxml:line_numbers
"
BAZEL_COMMAND_RUN_ARGUMENT="label-bin"
BAZEL_COMMAND_RUN_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--script_path=path
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_SHUTDOWN_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--iff_heap_size_greater_than=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--repository_cache=path
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_SYNC_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--configure
--noconfigure
--curses={yes,no,auto}
--deleted_packages=
--disk_cache=path
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--loading_phase_threads=
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--only=
--override_repository=
--package_path=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repository_cache=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
BAZEL_COMMAND_TEST_ARGUMENT="label-test"
BAZEL_COMMAND_TEST_FLAGS="
--action_env=
--all_incompatible_changes
--allow_analysis_failures
--noallow_analysis_failures
--analysis_testing_deps_limit=
--android_compiler=
--android_cpu=
--android_crosstool_top=label
--android_databinding_use_v3_4_args
--noandroid_databinding_use_v3_4_args
--android_dynamic_mode={off,default,fully}
--android_grte_top=label
--android_manifest_merger={legacy,android,force_android}
--android_manifest_merger_order={alphabetical,alphabetical_by_configuration,dependency}
--android_resource_shrinking
--noandroid_resource_shrinking
--android_sdk=label
--announce
--noannounce
--announce_rc
--noannounce_rc
--apk_signing_method={v1,v2,v1_v2}
--apple_bitcode=
--apple_compiler=
--apple_crosstool_top=label
--apple_enable_auto_dsym_dbg
--noapple_enable_auto_dsym_dbg
--apple_generate_dsym
--noapple_generate_dsym
--apple_grte_top=label
--apple_sdk=label
--aspects=
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--auto_cpu_environment_group=label
--auto_output_filter={none,all,packages,subpackages}
--bep_publish_used_heap_size_post_build
--nobep_publish_used_heap_size_post_build
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--break_build_on_parallel_dex2oat_failure
--nobreak_build_on_parallel_dex2oat_failure
--build
--nobuild
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_manual_tests
--nobuild_manual_tests
--build_metadata=
--build_python_zip={auto,yes,no}
--nobuild_python_zip
--build_runfile_links
--nobuild_runfile_links
--build_runfile_manifests
--nobuild_runfile_manifests
--build_tag_filters=
--build_test_dwp
--nobuild_test_dwp
--build_tests_only
--nobuild_tests_only
--cache_test_results={auto,yes,no}
--nocache_test_results
--catalyst_cpus=
--cc_output_directory_tag=
--cc_proto_library_header_suffixes=
--cc_proto_library_source_suffixes=
--check_constraint=
--check_licenses
--nocheck_licenses
--check_tests_up_to_date
--nocheck_tests_up_to_date
--check_up_to_date
--nocheck_up_to_date
--check_visibility
--nocheck_visibility
--collapse_duplicate_defines
--nocollapse_duplicate_defines
--collect_code_coverage
--nocollect_code_coverage
--color={yes,no,auto}
--combined_report={none,lcov}
--compilation_mode={fastbuild,dbg,opt}
--compile_one_dependency
--nocompile_one_dependency
--compiler=
--config=
--conlyopt=
--copt=
--coverage_report_generator=label
--coverage_support=label
--cpu=
--crosstool_top=label
--cs_fdo_absolute_path=
--cs_fdo_instrument=
--cs_fdo_profile=label
--curses={yes,no,auto}
--custom_malloc=label
--cxxopt=
--default_ios_provisioning_profile=label
--define=
--deleted_packages=
--desugar_for_android
--nodesugar_for_android
--device_debug_entitlements
--nodevice_debug_entitlements
--discard_analysis_cache
--nodiscard_analysis_cache
--disk_cache=path
--distdir=
--distinct_host_configuration
--nodistinct_host_configuration
--dynamic_mode={off,default,fully}
--embed_label=
--enable_apple_binary_native_protos
--noenable_apple_binary_native_protos
--enable_fdo_profile_absolute_path
--noenable_fdo_profile_absolute_path
--enable_platform_specific_config
--noenable_platform_specific_config
--enable_runfiles={auto,yes,no}
--noenable_runfiles
--enforce_constraints
--noenforce_constraints
--execution_log_binary_file=path
--execution_log_json_file=path
--expand_test_suites
--noexpand_test_suites
--experimental_action_listener=
--experimental_add_exec_constraints_to_targets=
--experimental_allow_android_library_deps_without_srcs
--noexperimental_allow_android_library_deps_without_srcs
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_android_compress_java_resources
--noexperimental_android_compress_java_resources
--experimental_android_databinding_v2
--noexperimental_android_databinding_v2
--experimental_android_resource_shrinking
--noexperimental_android_resource_shrinking
--experimental_android_rewrite_dexes_with_rex
--noexperimental_android_rewrite_dexes_with_rex
--experimental_android_use_parallel_dex2oat
--noexperimental_android_use_parallel_dex2oat
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cancel_concurrent_tests
--noexperimental_cancel_concurrent_tests
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_check_desugar_deps
--noexperimental_check_desugar_deps
--experimental_convenience_symlinks={normal,clean,ignore,log_only}
--experimental_convenience_symlinks_bep_event
--noexperimental_convenience_symlinks_bep_event
--experimental_delay_virtual_input_materialization
--noexperimental_delay_virtual_input_materialization
--experimental_desugar_java8_libs
--noexperimental_desugar_java8_libs
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_docker_image=
--experimental_docker_privileged
--noexperimental_docker_privileged
--experimental_docker_use_customized_images
--noexperimental_docker_use_customized_images
--experimental_docker_verbose
--noexperimental_docker_verbose
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_enable_docker_sandbox
--noexperimental_enable_docker_sandbox
--experimental_enable_flag_alias
--noexperimental_enable_flag_alias
--experimental_enable_objc_cc_deps
--noexperimental_enable_objc_cc_deps
--experimental_execution_log_file=path
--experimental_extra_action_filter=
--experimental_extra_action_top_level_only
--noexperimental_extra_action_top_level_only
--experimental_fetch_all_coverage_outputs
--noexperimental_fetch_all_coverage_outputs
--experimental_filter_library_jar_with_program_jar
--noexperimental_filter_library_jar_with_program_jar
--experimental_forward_instrumented_files_info_by_default
--noexperimental_forward_instrumented_files_info_by_default
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_guard_against_concurrent_changes
--noexperimental_guard_against_concurrent_changes
--experimental_import_deps_checking={off,warning,error}
--experimental_inmemory_dotd_files
--noexperimental_inmemory_dotd_files
--experimental_inmemory_jdeps_files
--noexperimental_inmemory_jdeps_files
--experimental_inprocess_symlink_creation
--noexperimental_inprocess_symlink_creation
--experimental_interleave_loading_and_analysis
--noexperimental_interleave_loading_and_analysis
--experimental_j2objc_header_map
--noexperimental_j2objc_header_map
--experimental_j2objc_shorter_header_path
--noexperimental_j2objc_shorter_header_path
--experimental_java_classpath={off,javabuilder,bazel}
--experimental_java_proto_add_allowed_public_imports
--noexperimental_java_proto_add_allowed_public_imports
--experimental_local_execution_delay=
--experimental_local_memory_estimate
--noexperimental_local_memory_estimate
--experimental_materialize_param_files_directly
--noexperimental_materialize_param_files_directly
--experimental_multi_cpu=
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_objc_enable_module_maps
--noexperimental_objc_enable_module_maps
--experimental_objc_fastbuild_options=
--experimental_objc_include_scanning
--noexperimental_objc_include_scanning
--experimental_omitfp
--noexperimental_omitfp
--experimental_oom_more_eagerly_threshold=
--experimental_persistent_javac
--experimental_persistent_test_runner
--noexperimental_persistent_test_runner
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_prefer_mutual_xcode
--noexperimental_prefer_mutual_xcode
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_proto_descriptor_sets_include_source_info
--noexperimental_proto_descriptor_sets_include_source_info
--experimental_proto_extra_actions
--noexperimental_proto_extra_actions
--experimental_remotable_source_manifests
--noexperimental_remotable_source_manifests
--experimental_remote_downloader=
--experimental_remote_grpc_log=path
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_repository_resolved_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_run_validations
--noexperimental_run_validations
--experimental_sandbox_async_tree_delete_idle_threads=
--experimental_sandboxfs_map_symlink_targets
--noexperimental_sandboxfs_map_symlink_targets
--experimental_sandboxfs_path=
--experimental_save_feature_state
--noexperimental_save_feature_state
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_spawn_scheduler
--experimental_split_xml_generation
--noexperimental_split_xml_generation
--experimental_starlark_cc_import
--noexperimental_starlark_cc_import
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_strict_fileset_output
--noexperimental_strict_fileset_output
--experimental_strict_java_deps={off,warn,error,strict,default}
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_use_llvm_covmap
--noexperimental_use_llvm_covmap
--experimental_use_sandboxfs={auto,yes,no}
--noexperimental_use_sandboxfs
--experimental_use_windows_sandbox={auto,yes,no}
--noexperimental_use_windows_sandbox
--experimental_verify_repository_rules=
--experimental_windows_sandbox_path=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_worker_max_multiplex_instances=
--experimental_worker_multiplex
--noexperimental_worker_multiplex
--experimental_workspace_rules_log_file=path
--explain=path
--explicit_java_test_deps
--noexplicit_java_test_deps
--extra_execution_platforms=
--extra_toolchains=
--fat_apk_cpu=
--fat_apk_hwasan
--nofat_apk_hwasan
--fdo_instrument=
--fdo_optimize=
--fdo_prefetch_hints=label
--fdo_profile=label
--features=
--fission=
--flag_alias=
--flaky_test_attempts=
--force_pic
--noforce_pic
--genrule_strategy=
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--grte_top=label
--high_priority_workers=
--host_action_env=
--host_compilation_mode={fastbuild,dbg,opt}
--host_compiler=
--host_conlyopt=
--host_copt=
--host_cpu=
--host_crosstool_top=label
--host_cxxopt=
--host_force_python={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--host_grte_top=label
--host_java_launcher=label
--host_java_toolchain=label
--host_javabase=label
--host_javacopt=
--host_linkopt=
--host_platform=label
--host_swiftcopt=
--http_timeout_scaling=
--ignore_unsupported_sandboxing
--noignore_unsupported_sandboxing
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_avoid_conflict_dlls
--noincompatible_avoid_conflict_dlls
--incompatible_default_to_explicit_init_py
--noincompatible_default_to_explicit_init_py
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_expand_if_all_available_in_flag_set
--noincompatible_disable_expand_if_all_available_in_flag_set
--incompatible_disable_native_android_rules
--noincompatible_disable_native_android_rules
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_legacy_py_provider
--noincompatible_disallow_legacy_py_provider
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_dont_enable_host_nonhost_crosstool_features
--noincompatible_dont_enable_host_nonhost_crosstool_features
--incompatible_enable_android_toolchain_resolution
--noincompatible_enable_android_toolchain_resolution
--incompatible_force_strict_header_check_from_starlark
--noincompatible_force_strict_header_check_from_starlark
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_make_thinlto_command_lines_standalone
--noincompatible_make_thinlto_command_lines_standalone
--incompatible_merge_genfiles_directory
--noincompatible_merge_genfiles_directory
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_compile_info_migration
--noincompatible_objc_compile_info_migration
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_prohibit_aapt1
--noincompatible_prohibit_aapt1
--incompatible_py2_outputs_are_suffixed
--noincompatible_py2_outputs_are_suffixed
--incompatible_py3_is_default
--noincompatible_py3_is_default
--incompatible_remote_results_ignore_disk
--noincompatible_remote_results_ignore_disk
--incompatible_remote_symlinks
--noincompatible_remote_symlinks
--incompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--noincompatible_remove_cpu_and_compiler_attributes_from_cc_toolchain
--incompatible_remove_legacy_whole_archive
--noincompatible_remove_legacy_whole_archive
--incompatible_remove_local_resources
--noincompatible_remove_local_resources
--incompatible_require_ctx_in_configure_features
--noincompatible_require_ctx_in_configure_features
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_strict_action_env
--noincompatible_strict_action_env
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_use_platforms_repo_for_constraints
--noincompatible_use_platforms_repo_for_constraints
--incompatible_use_python_toolchains
--noincompatible_use_python_toolchains
--incompatible_validate_top_level_header_inclusions
--noincompatible_validate_top_level_header_inclusions
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--incremental_dexing
--noincremental_dexing
--instrument_test_targets
--noinstrument_test_targets
--instrumentation_filter=
--interface_shared_objects
--nointerface_shared_objects
--ios_cpu=
--ios_memleaks
--noios_memleaks
--ios_minimum_os=
--ios_multi_cpus=
--ios_sdk_version=
--ios_signing_cert_name=
--ios_simulator_device=
--ios_simulator_version=
--j2objc_translation_flags=
--java_debug
--java_deps
--nojava_deps
--java_header_compilation
--nojava_header_compilation
--java_launcher=label
--java_toolchain=label
--javabase=label
--javacopt=
--jobs=
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--jvmopt=
--keep_going
--nokeep_going
--keep_state_after_build
--nokeep_state_after_build
--legacy_external_runfiles
--nolegacy_external_runfiles
--legacy_important_outputs
--nolegacy_important_outputs
--legacy_main_dex_list_generator=label
--legacy_whole_archive
--nolegacy_whole_archive
--linkopt=
--loading_phase_threads=
--local_cpu_resources=
--local_ram_resources=
--local_resources=
--local_termination_grace_seconds=
--local_test_jobs=
--logging=
--ltobackendopt=
--ltoindexopt=
--macos_cpus=
--macos_minimum_os=
--macos_sdk_version=
--materialize_param_files
--nomaterialize_param_files
--max_computation_steps=
--max_config_changes_to_show=
--max_test_output_bytes=
--memory_profile_stable_heap_parameters=
--message_translations=
--minimum_os_version=
--modify_execution_info=
--nested_set_depth_limit=
--objc_debug_with_GLIBCXX
--noobjc_debug_with_GLIBCXX
--objc_enable_binary_stripping
--noobjc_enable_binary_stripping
--objc_generate_linkmap
--noobjc_generate_linkmap
--objc_use_dotd_pruning
--noobjc_use_dotd_pruning
--objccopt=
--output_filter=
--output_groups=
--override_repository=
--package_path=
--parse_headers_verifies_modules
--noparse_headers_verifies_modules
--per_file_copt=
--per_file_ltobackendopt=
--persistent_android_resource_processor
--platform_mappings=path
--platform_suffix=
--platforms=
--plugin=
--print_relative_test_log_paths
--noprint_relative_test_log_paths
--process_headers_in_dependencies
--noprocess_headers_in_dependencies
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--progress_report_interval=
--proguard_top=label
--project_id=
--propeller_optimize=label
--proto_compiler=label
--proto_toolchain_for_cc=label
--proto_toolchain_for_j2objc=label
--proto_toolchain_for_java=label
--proto_toolchain_for_javalite=label
--protocopt=
--python2_path=
--python3_path=
--python_path=
--python_top=label
--python_version={py2,py3,py2and3,py2only,py3only,_internal_sentinel}
--remote_accept_cached
--noremote_accept_cached
--remote_allow_symlink_upload
--noremote_allow_symlink_upload
--remote_cache=
--remote_cache_header=
--remote_default_exec_properties=
--remote_default_platform_properties=
--remote_download_minimal
--remote_download_outputs={all,minimal,toplevel}
--remote_download_symlink_template=
--remote_download_toplevel
--remote_downloader_header=
--remote_exec_header=
--remote_execution_priority=
--remote_executor=
--remote_header=
--remote_instance_name=
--remote_local_fallback
--noremote_local_fallback
--remote_local_fallback_strategy=
--remote_max_connections=
--remote_proxy=
--remote_result_cache_priority=
--remote_retries=
--remote_timeout=
--remote_upload_local_results
--noremote_upload_local_results
--remote_verify_downloads
--noremote_verify_downloads
--repo_env=
--repository_cache=path
--run_under=
--runs_per_test=
--runs_per_test_detects_flakes
--noruns_per_test_detects_flakes
--sandbox_add_mount_pair=
--sandbox_base=
--sandbox_block_path=
--sandbox_debug
--nosandbox_debug
--sandbox_default_allow_network
--nosandbox_default_allow_network
--sandbox_fake_hostname
--nosandbox_fake_hostname
--sandbox_fake_username
--nosandbox_fake_username
--sandbox_tmpfs_path=
--sandbox_writable_path=
--save_temps
--nosave_temps
--share_native_deps
--noshare_native_deps
--shell_executable=path
--show_loading_progress
--noshow_loading_progress
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_result=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--spawn_strategy=
--stamp
--nostamp
--starlark_cpu_profile=
--strategy=
--strategy_regexp=
--strict_filesets
--nostrict_filesets
--strict_proto_deps={off,warn,error,strict,default}
--strict_system_includes
--nostrict_system_includes
--strip={always,sometimes,never}
--stripopt=
--subcommands={true,pretty_print,false}
--swiftcopt=
--symlink_prefix=
--target_environment=
--target_pattern_file=
--target_platform_fallback=label
--test_arg=
--test_env=
--test_filter=
--test_keep_going
--notest_keep_going
--test_lang_filters=
--test_output={summary,errors,all,streamed}
--test_result_expiration=
--test_runner_fail_fast
--notest_runner_fail_fast
--test_sharding_strategy={explicit,disabled}
--test_size_filters=
--test_strategy=
--test_summary={short,terse,detailed,none,testcase}
--test_tag_filters=
--test_timeout=
--test_timeout_filters=
--test_tmpdir=path
--test_verbose_timeout_warnings
--notest_verbose_timeout_warnings
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--toolchain_resolution_debug
--notoolchain_resolution_debug
--track_incremental_state
--notrack_incremental_state
--translations={auto,yes,no}
--notranslations
--trim_test_configuration
--notrim_test_configuration
--tvos_cpus=
--tvos_minimum_os=
--tvos_sdk_version=
--tvos_simulator_device=
--tvos_simulator_version=
--ui_actions_shown=
--ui_event_filters=
--use_ijars
--nouse_ijars
--use_singlejar_apkbuilder
--nouse_singlejar_apkbuilder
--verbose_explanations
--noverbose_explanations
--verbose_failures
--noverbose_failures
--verbose_test_summary
--noverbose_test_summary
--watchfs
--nowatchfs
--watchos_cpus=
--watchos_minimum_os=
--watchos_sdk_version=
--watchos_simulator_device=
--watchos_simulator_version=
--worker_extra_flag=
--worker_max_instances=
--worker_quit_after_build
--noworker_quit_after_build
--worker_sandboxing
--noworker_sandboxing
--worker_verbose
--noworker_verbose
--workspace_status_command=path
--xbinary_fdo=label
--xcode_version=
--xcode_version_config=label
"
BAZEL_COMMAND_VERSION_FLAGS="
--all_incompatible_changes
--announce_rc
--noannounce_rc
--attempt_to_print_relative_paths
--noattempt_to_print_relative_paths
--bes_backend=
--bes_best_effort
--nobes_best_effort
--bes_keywords=
--bes_lifecycle_events
--nobes_lifecycle_events
--bes_outerr_buffer_size=
--bes_outerr_chunk_size=
--bes_proxy=
--bes_results_url=
--bes_timeout=
--build_event_binary_file=
--build_event_binary_file_path_conversion
--nobuild_event_binary_file_path_conversion
--build_event_json_file=
--build_event_json_file_path_conversion
--nobuild_event_json_file_path_conversion
--build_event_max_named_set_of_file_entries=
--build_event_publish_all_actions
--nobuild_event_publish_all_actions
--build_event_text_file=
--build_event_text_file_path_conversion
--nobuild_event_text_file_path_conversion
--build_metadata=
--color={yes,no,auto}
--config=
--curses={yes,no,auto}
--distdir=
--enable_platform_specific_config
--noenable_platform_specific_config
--experimental_allow_tags_propagation
--noexperimental_allow_tags_propagation
--experimental_announce_profile_path
--noexperimental_announce_profile_path
--experimental_build_event_expand_filesets
--noexperimental_build_event_expand_filesets
--experimental_build_event_fully_resolve_fileset_symlinks
--noexperimental_build_event_fully_resolve_fileset_symlinks
--experimental_build_event_upload_strategy=
--experimental_cc_shared_library
--noexperimental_cc_shared_library
--experimental_disable_external_package
--noexperimental_disable_external_package
--experimental_enable_android_migration_apis
--noexperimental_enable_android_migration_apis
--experimental_generate_json_trace_profile
--noexperimental_generate_json_trace_profile
--experimental_google_legacy_api
--noexperimental_google_legacy_api
--experimental_multi_threaded_digest
--noexperimental_multi_threaded_digest
--experimental_ninja_actions
--noexperimental_ninja_actions
--experimental_oom_more_eagerly_threshold=
--experimental_platforms_api
--noexperimental_platforms_api
--experimental_profile_additional_tasks=
--experimental_profile_cpu_usage
--noexperimental_profile_cpu_usage
--experimental_profile_include_primary_output
--noexperimental_profile_include_primary_output
--experimental_profile_include_target_label
--noexperimental_profile_include_target_label
--experimental_repo_remote_exec
--noexperimental_repo_remote_exec
--experimental_repository_cache_hardlinks
--noexperimental_repository_cache_hardlinks
--experimental_repository_hash_file=
--experimental_resolved_file_instead_of_workspace=
--experimental_scale_timeouts=
--experimental_sibling_repository_layout
--noexperimental_sibling_repository_layout
--experimental_starlark_config_transitions
--noexperimental_starlark_config_transitions
--experimental_stream_log_file_uploads
--noexperimental_stream_log_file_uploads
--experimental_ui_max_stdouterr_bytes=
--experimental_ui_mode={oldest_actions,mnemonic_histogram}
--experimental_verify_repository_rules=
--experimental_windows_watchfs
--noexperimental_windows_watchfs
--experimental_workspace_rules_log_file=path
--gnu_format
--nognu_format
--google_auth_scopes=
--google_credentials=
--google_default_credentials
--nogoogle_default_credentials
--grpc_keepalive_time=
--grpc_keepalive_timeout=
--http_timeout_scaling=
--incompatible_always_check_depset_elements
--noincompatible_always_check_depset_elements
--incompatible_depset_for_libraries_to_link_getter
--noincompatible_depset_for_libraries_to_link_getter
--incompatible_disable_depset_items
--noincompatible_disable_depset_items
--incompatible_disable_target_provider_fields
--noincompatible_disable_target_provider_fields
--incompatible_disable_third_party_license_checking
--noincompatible_disable_third_party_license_checking
--incompatible_disallow_empty_glob
--noincompatible_disallow_empty_glob
--incompatible_disallow_legacy_javainfo
--noincompatible_disallow_legacy_javainfo
--incompatible_disallow_struct_provider_syntax
--noincompatible_disallow_struct_provider_syntax
--incompatible_do_not_split_linking_cmdline
--noincompatible_do_not_split_linking_cmdline
--incompatible_java_common_parameters
--noincompatible_java_common_parameters
--incompatible_linkopts_to_linklibs
--noincompatible_linkopts_to_linklibs
--incompatible_new_actions_api
--noincompatible_new_actions_api
--incompatible_no_attr_license
--noincompatible_no_attr_license
--incompatible_no_implicit_file_export
--noincompatible_no_implicit_file_export
--incompatible_no_rule_outputs_param
--noincompatible_no_rule_outputs_param
--incompatible_objc_provider_remove_compile_info
--noincompatible_objc_provider_remove_compile_info
--incompatible_require_linker_input_cc_api
--noincompatible_require_linker_input_cc_api
--incompatible_restrict_string_escapes
--noincompatible_restrict_string_escapes
--incompatible_run_shell_command_string
--noincompatible_run_shell_command_string
--incompatible_string_replace_count
--noincompatible_string_replace_count
--incompatible_use_cc_configure_from_rules_cc
--noincompatible_use_cc_configure_from_rules_cc
--incompatible_visibility_private_attributes_at_definition
--noincompatible_visibility_private_attributes_at_definition
--json_trace_compression={auto,yes,no}
--nojson_trace_compression
--keep_state_after_build
--nokeep_state_after_build
--legacy_important_outputs
--nolegacy_important_outputs
--logging=
--max_computation_steps=
--memory_profile_stable_heap_parameters=
--nested_set_depth_limit=
--override_repository=
--profile=path
--progress_in_terminal_title
--noprogress_in_terminal_title
--project_id=
--repository_cache=path
--show_progress
--noshow_progress
--show_progress_rate_limit=
--show_task_finish
--noshow_task_finish
--show_timestamps
--noshow_timestamps
--slim_profile
--noslim_profile
--starlark_cpu_profile=
--tls_certificate=
--tls_client_certificate=
--tls_client_key=
--tool_tag=
--track_incremental_state
--notrack_incremental_state
--ui_actions_shown=
--ui_event_filters=
--watchfs
--nowatchfs
"
