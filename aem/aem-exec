#!/bin/bash
#
###############################################################################
# Copyright 2024 The Apollo Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Flags
#set -u
#set -e
set -x

# load basic functions
if [[ ! "${AEM_INITED}" == 1 ]]; then
  home_dir="$(dirname "$(realpath "$0")")"
  # shellcheck source=./run.sh
  source "${home_dir}/run.sh"
fi

usage() {
  echo "Usage: aem exec [OPTIONS] -- COMMAND [ARG...]
Options:
  -h, --help          Print usage
  -n, --name string   Name of the environment
"
}

parse_arguments() {
  args=()
  while [[ "$#" -gt 0 ]]; do
    local opt="$1"
    shift
    case "${opt}" in
      -h | --help)
        usage
        exit 0
        ;;
      -n | --name)
        export APOLLO_ENV_NAME="$1"
        export APOLLO_ENV_CONTAINER_NAME="${APOLLO_ENV_CONTAINER_PREFIX}${APOLLO_ENV_NAME}"
        shift
        ;;
      --)
        args+=("$@")
        break
        ;;
      -*)
        error "unsupported option: ${opt}"
        exit 1
        ;;
      *)
        error "unsupported option: ${opt}"
        exit 1
        ;;
    esac
  done
  export APOLLO_ENV_EXEC_ARGS=("${args[@]}")
}

execute() {

  # load custom environment variables
  [[ -f "${PWD}/.env" ]] && set -a && source "${PWD}/.env" && set +a

  parse_arguments "$@"

  if ! apollo_env_exists "${APOLLO_ENV_NAME}"; then
    fatal "environment ${APOLLO_ENV_NAME} does not exist"
    exit 1
  else
    set -a
    source "${APOLLO_ENVS_ROOT}/${APOLLO_ENV_NAME}/env.config"
    set +a
  fi

  apollo_exec_env "${APOLLO_ENV_EXEC_ARGS[@]}"
}

execute "$@"
