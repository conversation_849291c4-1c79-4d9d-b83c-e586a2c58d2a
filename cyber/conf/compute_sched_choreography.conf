scheduler_conf {
  policy: "choreography"

  choreography_conf {
    choreography_processor_num: 8
    choreography_affinity: "range"
    choreography_cpuset: "0-7"

    pool_processor_num: 12
    pool_affinity: "range"
    pool_cpuset: "8-11,16-23"

    tasks: [
      {
        name: "velodyne_128_convert"
        processor: 0
        prio: 11
      },
      {
        name: "velodyne128_compensator"
        processor: 1
        prio: 12
      },
      {
        name: "velodyne_16_front_center_convert"
        processor: 2
      },
      {
        name: "velodyne_fusion"
        processor: 2
        prio: 2
      },
      {
        name: "velodyne16_fusion_compensator"
        processor: 2
        prio: 3
      },
      {
        name: "velodyne_16_rear_left_convert"
        prio: 10
      },
      {
        name: "velodyne_16_rear_right_convert"
        prio: 10
      },
      {
        name: "Velodyne128Detection"
        processor: 3
        prio: 13
      },
      {
        name: "Velodyne16Detection"
        processor: 3
        prio: 10
      },
      {
        name: "RecognitionComponent"
        processor: 3
        prio: 14
      },
      {
        name: "SensorFusion"
        processor: 4
        prio: 15
      },
      {
        name: "prediction"
        processor: 5
        prio: 16
      },
      {
        name: "planning"
        processor: 6
        prio: 17
      },
      {
        name: "routing"
        processor: 6
      },
      {
        name: "planning_/apollo/routing_response"
        processor: 6
      },
      {
        name: "planning_/apollo/perception/traffic_light"
        processor: 7
        prio: 17
      },
      {
        name: "msf_localization"
        processor: 7
      },
      {
        name: "rtk_localization"
        processor: 7
      },
      {
        name: "msf_localization_/apollo/sensor/lidar64/compensator/PointCloud2"
        prio: 4
      },
      {
        name: "camera_front_6mm_compress"
        prio: 3
      },
      {
        name: "camera_front_12mm_compress"
        prio: 3
      },
      {
        name: "camera_left_fisheye_compress"
        prio: 3
      },
      {
        name: "camera_right_fisheye_compress"
        prio: 3
      },
      {
        name: "camera_rear_6mm_compress"
        prio: 3
      }
    ]
  }
}
