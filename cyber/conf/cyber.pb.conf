# transport_conf {
#     shm_conf {
#         # "multicast" "condition"
#         notifier_type: "condition"
#         # "posix" "xsi"
#         shm_type: "xsi"
#         shm_locator {
#             ip: "*************"
#             port: 8888
#         }
#     }
#     participant_attr {
#         lease_duration: 12
#         announcement_period: 3
#         domain_id_gain: 200
#         port_base: 10000
#     }
#     communication_mode {
#         same_proc: INTRA
#         diff_proc: SHM
#         diff_host: RTPS
#     }
#     resource_limit {
#         max_history_depth: 1000
#     }
# }
#
transport_conf {
  communication_mode {
    same_proc: INTRA
    diff_proc: SHM
    diff_host: RTPS
  }
  participant_attr {
    lease_duration: 12
    announcement_period: 3
    domain_id_gain: 250
    port_base: 7400
  }
  shm_conf {
    arena_shm_conf {
      arena_channel_conf {
        channel_name: "/apollo/msg"
        max_msg_size: 33554432
        max_pool_size: 32
      }
    }
  }
}

run_mode_conf {
    run_mode: MODE_REALITY
    clock_mode: MODE_CYBER
}

scheduler_conf {
    routine_num: 100
    default_proc_num: 16
}
