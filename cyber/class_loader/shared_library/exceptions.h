/******************************************************************************
 * Copyright 2020 The Apollo Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *****************************************************************************/

#ifndef CYBER_CLASS_LOADER_SHARED_LIBRARY_EXCEPTIONS_H_
#define CYBER_CLASS_LOADER_SHARED_LIBRARY_EXCEPTIONS_H_

#include <stdexcept>
#include <string>

namespace apollo {
namespace cyber {
namespace class_loader {

#define DECLARE_SHARED_LIBRARY_EXCEPTION(CLS, BASE)             \
  class CLS : public BASE {                                     \
   public:                                                      \
    explicit CLS(const std::string& err_msg) : BASE(err_msg) {} \
    ~CLS() throw() {}                                           \
  };

DECLARE_SHARED_LIBRARY_EXCEPTION(LibraryAlreadyLoadedException,
                                 std::runtime_error);
DECLARE_SHARED_LIBRARY_EXCEPTION(LibraryLoadException, std::runtime_error);
DECLARE_SHARED_LIBRARY_EXCEPTION(SymbolNotFoundException, std::runtime_error);

}  // namespace class_loader
}  // namespace cyber
}  // namespace apollo

#endif  // CYBER_CLASS_LOADER_SHARED_LIBRARY_EXCEPTIONS_H_
