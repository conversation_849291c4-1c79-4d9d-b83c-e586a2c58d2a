syntax = "proto2";

package apollo.cyber.examples.proto;

message SamplesTest1 {
  optional string class_name = 1;
  optional string case_name = 2;
};

message Chatter {
  optional uint64 timestamp = 1;
  optional uint64 lidar_timestamp = 2;
  optional uint64 seq = 3;
  optional bytes content = 4;
};

message Driver {
  optional string content = 1;
  optional uint64 msg_id = 2;
  optional uint64 timestamp = 3;
};
